import json
import os
import re
import time
from datetime import datetime, timedelta
from functools import lru_cache

import numpy as np
import pandas as pd
from celery import Celery
from jinja2 import Template
# from pdfreport import PDFReport
from pathos.multiprocessing import ProcessingPool as Pool

import report.utils as utils
from core_utils.api_client import APIClient
from core_utils.dictionary import DICTIONARY
from mail import EmailServices

PERIOR_TIME = 31 * 6

T_time = 3
app = Celery(
    'b_jobs',
    broker='redis://localhost:6379/0',  # Replace 'localhost' with the Docker host IP
    backend='redis://localhost:6379/0'
)


class ReportGenerator:
    """
    A Service will build daily report from evaluating all stocks and find the signals by filters. Using Report
    to render the pdf and send email by Sendgrid (Email Service)
    """

    def __init__(self, watch_for_sell_tickers: list, alert_monitor_tickers: dict, profile_monitor_tickers: dict,
                 used_indicators: list, f_path='../ticker_v1a/', filter_path="filter.json", cutloss=0.15):
        self.filter = self.load_custom_filters(filter_path)
        self.weight = self.load_custom_filters("filter_weight.json")

        self.buy_2_sell = self.buy_map_sell()
        self.sell_2_buy = self.sell_map_buy()
        self.cutloss = cutloss
        self.FPATH = f_path
        # self.pdf_report = PDFReport
        self.watch_for_sell_tickers = watch_for_sell_tickers
        # Ticker have null indicator
        self.missing_indicators_tickers = alert_monitor_tickers
        # Ticker miss nessary indicator (indicator in report profile)
        self.missing_used_indicators_tickers = profile_monitor_tickers
        self.output_path = "assets/report.pdf"
        self.html_sample = 'html_template.html'
        self.lookback = 10
        self.exp = 1

        self.used_indicators = list(set(used_indicators + ['time', 'Open_1D', 'ticker', 'Volume_1M_P50']))

    @staticmethod
    def load_custom_filters(path="filter.json"):
        custom_filter = {}
        if os.path.exists(path):
            with open(path, "r") as data:
                custom_filter = json.load(data)
        if 'Init' in custom_filter.keys():
            for key, value in custom_filter.items():
                custom_filter[key] = value.replace("{Init}", custom_filter['Init'])
        return custom_filter

    def buy_map_sell(self):
        all_filters = []
        pre_pattern = []

        for f in self.filter:
            if f.startswith('~'):
                all_filters.append(f[1:])

        buy2sell = {
            "all": list(set(all_filters))
        }

        for f in self.filter:
            if f.startswith("$"):
                sell_key = f[1:]
                value = [x.replace("~", "").strip() for x in self.filter[f].split(",")]
                buy2sell[sell_key] = value
            if f.startswith('#'):
                sell_key = f[1:]
                buy2sell[sell_key] = self.filter[f]
                pre_pattern.append(sell_key)

        buy2sell['pre_pattern'] = pre_pattern

        return buy2sell

    def sell_map_buy(self):
        """Revert from `self.buy_2_sell` to `self.sell_2_buy`."""
        s2b = {}
        for buy, sells in self.buy_2_sell.items():
            for sell in sells:
                if sell in s2b:
                    s2b[sell].append(buy)
                else:
                    s2b[sell] = [buy]
        return s2b

    def _get_filter_indicators(self):
        indicators = {}
        for key, value in self.filter.items():
            if key.startswith('_') or key.startswith('~'):
                indicator = re.findall(r'\b[A-Za-z_]\w*\b', value)
                indicator = [var for var in indicator if not var.isdigit() and var.lower() not in
                             ('and', 'or', 'not', 'if', 'else', 'true', 'false', 'null', 'none', 'in', 'is', 'ge', 'le',
                              'gt', 'lt', 'eq', 'ne', '>=', '<=', '>', '<', '==', '!=', '(', ')', '[', ']', '{', '}',
                              '&', '|', 'abs', 'acos', 'acosh', 'asin', 'asinh', 'atan', 'atanh', 'ceil', 'cos',
                              'cosh')]

                indicator_set = sorted(list(filter(lambda x: x != "time", set(indicator))))
                indicator_set.insert(0, "time")
                indicator_set.insert(0, "filter")
                indicators[key] = indicator_set

        return indicators

    def _evaluate_index(self, df_latest):
        def analysis_pattern(df_index_tail):
            index_pattern = {
                "~BearDvgVNI1": "(D_RSI_Max1W/D_RSI > 1.044)  & (D_RSI_Max3M > 0.74) & (D_RSI_Max1W < 0.72) & (D_RSI_Max1W>0.61) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.028) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.11) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.96) & (D_RSI_MinT3 > 0.43) & (D_CMF < 0.13)",
                "~BearDvgVNI2": "(D_RSI_Max1W/D_RSI > 1.016)  & (D_RSI_Max3M > 0.77) & (D_RSI_Max1W < 0.79) & (D_RSI_Max1W>0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.008) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.1) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.97) & (D_RSI_MinT3 > 0.5) & (D_CMF < 0.15)"
            }
            results = []
            for k, v in index_pattern.items():
                count_invalid = 0
                str_result = f'{k}: Not match\n'
                list_query = [q.strip(" ()") for q in v.split('&')]
                for q in list_query:
                    is_valid = df_index_tail.query(q)
                    if not is_valid.empty:
                        continue

                    str_result += f'    Condition: {q} | Value: '
                    for m_q in re.findall(r"[A-Za-z_]\w*", q):
                        str_result += f'{m_q}: {df_index_tail[m_q].values[0]:.2f}, '
                    str_result += '\n'
                    count_invalid += 1

                if 0 < count_invalid <= 3:
                    results.append(str_result)

                if count_invalid == 0:
                    results.append(f'{k}: Hit pattern\n')

            return results

        def index_capitalization(df_latest):
            df_latest['m_cap'] = df_latest['OShares'] * df_latest['Price']
            return df_latest['m_cap'].sum()

        def filter_and_group(df_vnindex, col, threshold, comparison, df_percentiles, id_percentile='90%'):
            df_vnindex = df_vnindex.copy()
            df_vnindex['s_time'] = pd.to_datetime(df_vnindex['time'])

            condition = f"{col} >= {threshold}" if comparison == "greater" else f"{col} <= {threshold}"
            df_filtered = df_vnindex.query(condition).copy()
            df_filtered['start_group'] = df_filtered['s_time']
            df_filtered['end_group'] = df_filtered['s_time']

            df_base = df_vnindex[['s_time', 'time', 'Open_1D', 'Close']].copy()
            df_base = df_base.sort_values('s_time').reset_index(drop=True)

            for i_group, row_a in df_filtered.iterrows():
                start_group = row_a['start_group']
                try:
                    start_index = df_base.index[df_base['s_time'] == start_group][0]
                except:
                    start_index = pd.merge_asof(
                        pd.DataFrame({'s_time': [start_group]}).sort_values('s_time'),
                        df_base[['s_time']].sort_values('s_time'),
                        on='s_time', direction='backward'
                    ).index[0]

                break_count = 0
                for per in df_percentiles.columns:
                    try:
                        session = per.split('P')[1]
                        int(session)
                    except:
                        continue

                    try:
                        cur = df_base.iloc[start_index + int(session)]['Open_1D']
                        base = df_base.iloc[start_index]['Open_1D']
                        ret = (cur / base - 1) * 100

                        if ret <= df_percentiles.loc['90%', per]:
                            break_count += 1
                        else:
                            break_count = 0

                        if break_count == 4:
                            break
                    except Exception as error:
                        print(f"Error: {error} - at {start_group}, session {session}")
                        break

                try:
                    session_break = int(session) - break_count * 5
                    offset_dates = df_base.iloc[start_index + int(session_break)]['time']
                except:
                    offset_dates = df_base.iloc[-1]['time']

                df_filtered.at[i_group, 'end_group'] = offset_dates

            df_filtered['month'] = pd.to_datetime(df_filtered['month'])

            # Group consecutive months (month_diff <= 1)
            df_filtered['month_diff'] = round(df_filtered['month'].diff().dt.days / 31).fillna(0)
            df_filtered['group'] = (df_filtered['month_diff'] > 1).cumsum()
            df_filtered['start_group'] = df_filtered.groupby('group')['start_group'].transform('min')
            df_filtered['end_group'] = df_filtered.groupby('group')['end_group'].transform('max')

            df_filtered = df_filtered.drop_duplicates(subset=['group'], keep='first')

            # # Group consecutive group (group_diff <= 1)
            df_filtered['group_diff'] = (df_filtered['start_group'] - df_filtered['end_group'].shift(1)).dt.days / 31
            df_filtered['group'] = (df_filtered['group_diff'] > 1).cumsum()
            df_filtered['start_group'] = df_filtered.groupby('group')['start_group'].transform('min')
            df_filtered['end_group'] = df_filtered.groupby('group')['end_group'].transform('max')

            df_filtered = df_filtered.drop_duplicates(subset=['group'], keep='first')

            return df_filtered.tail(1)[['start_group', 'end_group']]

        df_index = pd.read_csv(f'{self.FPATH}/VNINDEX.csv', dtype={'time': str, 'ticker': str})
        # df_index = df_index[-500:].reset_index(drop=True)

        df_index['s_time'] = pd.to_datetime(df_index['time'])
        df_index['month'] = df_index['s_time'].dt.strftime('%Y-%m')

        df_index['TD_MA1M'] = df_index['Trading_Session'].rolling(20, min_periods=5).mean()
        df_index['TD_MA1M_center'] = df_index['Trading_Session'].rolling(20, center=True, min_periods=5).mean()
        df_index['PE/PE_MA5Y'] = df_index['VNINDEX_PE'] / df_index['VNINDEX_PE_MA5Y']

        df_index['profit_3M'] = 100 * (df_index['Close'] / df_index['Close'].shift(60) - 1)
        df_index['ratio_TD_MA1M'] = 100 * ((df_index['TD_MA1M'] / df_index['TD_MA1M_center'].shift(60)) - 1)
        df_index['ratio_PE_PE_MA5Y'] = 100 * ((df_index['PE/PE_MA5Y'] / df_index['PE/PE_MA5Y'].shift(60)) - 1)

        df_index['P3M'] = 100 * (df_index['O3M'] - 1)

        # Prepare for overbuy, oversell
        # percentile
        percentiles = [0.1, 0.2, 0.8, 0.9, 0.95]

        dict_percentiles = {
            'P3M': df_index['P3M'].quantile(percentiles).values,
            'VNINDEX_PE': df_index['VNINDEX_PE'].quantile(percentiles).values,
        }

        for i in range(60, 245, 5):
            df_index[f'P{i}'] = 100 * ((df_index['Open_1D'].shift(-i) / df_index['Open_1D']) - 1)
            dict_percentiles[f'P{i}'] = df_index[f'P{i}'].quantile(percentiles).values

        df_percentiles = pd.DataFrame(dict_percentiles, index=[f'{int(p * 100)}%' for p in percentiles])

        over_params = [
            {'col': 'P3M', 'threshold': df_percentiles.loc['95%', 'P3M'], 'comparison': "greater",
             'id_percentile': '95%'},
            {'col': 'P3M', 'threshold': df_percentiles.loc['90%', 'P3M'], 'comparison': "greater",
             'id_percentile': '90%'},
            {'col': 'P3M', 'threshold': df_percentiles.loc['10%', 'P3M'], 'comparison': "less", 'id_percentile': '10%'},
            {'col': 'P3M', 'threshold': df_percentiles.loc['80%', 'P3M'], 'comparison': "greater",
             'id_percentile': '80%'},
            {'col': 'P3M', 'threshold': -df_percentiles.loc['20%', 'P3M'], 'comparison': "less",
             'id_percentile': '20%'},
        ]
        now = pd.Timestamp.now()
        found_flag = False

        df_index_500 = df_index[-500:].reset_index(drop=True)
        for param in over_params:
            pd_period = filter_and_group(df_index_500, **param, df_percentiles=df_percentiles)
            if pd_period is None or pd_period.empty:
                continue
            if abs(pd_period['end_group'].iloc[0] - now) < pd.Timedelta(days=15):
                found_flag = True
                break

        if found_flag:
            if param['id_percentile'] == '95%':
                p_types = f'overbuy above {param["id_percentile"]} percentile'
            elif param['id_percentile'] == '90%':
                p_types = f'overbuy above {param["id_percentile"]} percentile'
            elif param['id_percentile'] == '10%':
                p_types = f'oversell below {param["id_percentile"]} percentile'
            elif param['id_percentile'] == '80%':
                p_types = f'overbuy above {param["id_percentile"]} percentile'
            elif param['id_percentile'] == '20%':
                p_types = f'oversell below {param["id_percentile"]} percentile'
            else:
                p_types = 'unknown'

            begin = pd_period['start_group'].iloc[0].strftime('%Y-%m-%d')
            end = pd_period['end_group'].iloc[0].strftime('%Y-%m-%d')
            profit = df_index.loc[df_index['time'] == end, 'Close'].values[0] / df_index.loc[
                df_index['time'] == begin, 'Close'].values[0] - 1

            overheated = {
                'start_group': begin,
                'end_group': end,
                'profit': self.convert_to_sci_notation(100 * profit),
                'type': p_types
            }

        # percentile

        # PE
        is_pe = None
        if df_index['VNINDEX_PE'].dropna().iloc[-1] > df_percentiles.loc['95%', 'VNINDEX_PE']:
            is_pe = 'PE above 95% percentile'
        elif df_index['VNINDEX_PE'].dropna().iloc[-1] > df_percentiles.loc['90%', 'VNINDEX_PE']:
            is_pe = 'PE above 90% percentile'
        elif df_index['VNINDEX_PE'].dropna().iloc[-1] < df_percentiles.loc['10%', 'VNINDEX_PE']:
            is_pe = 'PE below 10% percentile'
        elif df_index['VNINDEX_PE'].dropna().iloc[-1] < df_percentiles.loc['20%', 'VNINDEX_PE']:
            is_pe = 'PE below 20% percentile'

        # Buffett Indicator
        PERCENTILES = {
            'BFI_95': 20157.19,
            'BFI_90': 18086.76,
            'BFI_10': 5472.60,
            'BFI_20': 8258.36,
        }
        is_bfi = None
        GDP = 476388230307
        BFI = index_capitalization(df_latest) / GDP
        if BFI > PERCENTILES['BFI_95']:
            is_bfi = 'Buffett Indicator above 95% percentile'
        elif BFI > PERCENTILES['BFI_90']:
            is_bfi = 'Buffett Indicator above 90% percentile'
        elif BFI < PERCENTILES['BFI_10']:
            is_bfi = 'Buffett Indicator below 10% percentile'
        elif BFI < PERCENTILES['BFI_20']:
            is_bfi = 'Buffett Indicator below 20% percentile'

        # Index pattern
        index_pattern = analysis_pattern(df_index.tail(1))

        return {
            'pe_today': self.convert_to_sci_notation(df_index['VNINDEX_PE'].dropna().iloc[-1]),
            'pe_pe5y_today': self.convert_to_sci_notation(df_index['PE/PE_MA5Y'].dropna().iloc[-1]),
            'bfi_today': self.convert_to_sci_notation(BFI),
            'close_rat': self.convert_to_sci_notation(df_index['profit_3M'].dropna().iloc[-1]),
            'trading_value_rat': self.convert_to_sci_notation(df_index['ratio_TD_MA1M'].dropna().iloc[-1]),
            'pe_ratio': self.convert_to_sci_notation(df_index['ratio_PE_PE_MA5Y'].dropna().iloc[-1]),
            'overheated': overheated if found_flag else None,
            'is_pe': is_pe,
            'is_bfi': is_bfi,
            'index_pattern': '\n'.join(index_pattern) if index_pattern else None
        }

    @staticmethod
    def convert_to_sci_notation(val):
        """
        Convert a number to scientific notation.
        Parameters
        ----------
        val : float
            The number to be converted.
        Returns
        -------
        str
            The number in scientific notation, as a string.
        Examples
        --------
        # >>> ReportBuilder.convert_to_sci_notation(12345)
        '1.235e+04'
        """
        if val is None:
            return 'N/A'

        if val == 0:
            return '0'

        try:
            val = float(val)
        except (ValueError, TypeError):
            return 'N/A'

        if np.isnan(val):
            return "N/A"

        if np.isinf(val):
            return "N/A"

        exponent = int(np.floor(np.log10(abs(val))))
        mantissa = val / 10 ** exponent

        if exponent >= 5:
            return f'{mantissa:.2f}e{exponent}'

        return f'{val:.2f}' if val != int(val) else f'{val:.0f}'

    @staticmethod
    def convert_to_kvnd_notation(val):
        if val is None:
            return 'N/A'

        if val == 0:
            return '0'

        if isinstance(val, str):
            try:
                val = float(val)
            except ValueError:
                return val

        if not isinstance(val, (int, float)):
            return 'N/A'

        if np.isnan(val):
            return np.nan

        return f"{val / 1000:.1f}k"

    def eval_filter_ticker(self, pdxx, ticker):
        """
        Evaluate filter for a ticker
        Input:
            pdxx: ticker daily series
            dictFilter: dictionary of filters
            CUTLOSS: cutloss threshold
        Output:
            pdy: dataframe of latest hit
        """

        def find_cutloss_id(_pd):
            _pd = _pd.copy()
            _pd['cutloss_price'] = _pd['Open_1D'] * (1 - self.cutloss)

            values = _pd['Close'].values
            cutloss_price = _pd['cutloss_price'].values

            n = len(values)
            result = np.full(n, np.nan)

            for i in range(n):
                window_data = values[i:]
                cutloss_value = cutloss_price[i]

                idx = np.where(window_data <= cutloss_value)[0]
                if len(idx) > 0:
                    result[i] = i + idx[0]

            return result

        # Initial
        pd_all = pdxx.copy()
        pd_all['curr_close'] = pd_all.iloc[-1]['Close']
        pd_all['curr_volume'] = pd_all.iloc[-1]['Volume_1M_P50']

        # Finding cutloss
        pd_all['cutloss_id'] = find_cutloss_id(pd_all)
        for i in range(pd_all.shape[0]):
            cutloss_id = pd_all.loc[i, 'cutloss_id']
            if not pd.isna(cutloss_id):
                pd_all.loc[i, 'cutloss_time'] = pd_all.loc[cutloss_id, 'time']
                pd_all.loc[i, 'cutloss_close'] = pd_all.loc[cutloss_id, 'Close']

        ly = []
        # sell_filters and buy filters
        for f in self.filter:
            try:
                if f.startswith('~') or f.startswith('_'):
                    pdx = pd_all.query(f'({self.filter[f]})').sort_values('time', ascending=False).copy()
                    pdx['filter'] = f
                    ly.append(pdx)
            except:
                continue

        df = pd.concat(ly, axis=0).sort_values('time', ascending=True)
        df['idx'] = df.index

        # Add score
        df['score'] = 0
        for i in range(df.shape[0]):
            now_idx = df['idx'].iloc[i]
            lookback_idx = now_idx - self.lookback
            slice = df.query('idx >= @lookback_idx & idx <= @now_idx')
            filters = slice['filter'].unique().tolist()
            df.loc[df.index[i], 'score'] = sum([self.weight.get(f, 0) for f in filters])

        # Preprocessing
        serial_items = []
        serial_sell_items = []
        for i in range(df.shape[0]):
            row = df.iloc[i]
            filter = row['filter']
            if filter.startswith('_'):
                serial_items.append(row)
            elif filter.startswith('~'):
                serial_sell_items.append(row)
                serial_items = [
                    hit for hit in serial_items
                    if filter[1:] not in self.buy_2_sell.get(hit['filter'][1:], [])
                ]

        serial_items.extend(serial_sell_items)
        keep_df = pd.DataFrame(serial_items)

        return keep_df

    def _evaluate_filters(self, processed_tickers):
        """Return dataframe of latest hits."""
        now = datetime.now()
        since = now - timedelta(days=PERIOR_TIME)

        def _eval(ticker):
            try:
                dfs = []
                for chunk in pd.read_csv(f'{self.FPATH}/{ticker}.csv',
                                         chunksize=1000):
                    chunk = chunk[chunk['time'] >= since.strftime("%Y-%m")]
                    if not chunk.empty:
                        dfs.append(chunk)

                if not dfs:
                    return None

                data = pd.concat(dfs, ignore_index=True)
                result = self.eval_filter_ticker(data, ticker)

                # workaround get last row of each ticker
                last_row = data.tail(1).copy()

                return result, last_row
            except Exception as error:
                print(f"Error processing ticker {ticker}: {str(error)}")

        num_procs = 10
        lres = []
        last_rows = []

        with Pool(num_procs) as p:
            for res in p.uimap(_eval, processed_tickers):
                if res is None:
                    continue
                result, last_row = res
                if result is not None and not result.empty:
                    lres.append(result)
                if last_row is not None and not last_row.empty:
                    last_rows.append(last_row)

        if lres:
            df = pd.concat(lres, axis=0).sort_values('time', ascending=False).reset_index(drop=True)
            df.to_csv('eval_pd.csv', index=False)
        else:
            df = pd.DataFrame()

        if last_rows:
            df_last = pd.concat(last_rows, axis=0).reset_index(drop=True)
            df_last.to_csv('last_rows.csv', index=False)
        else:
            df_last = pd.DataFrame()

        return df, df_last

    def _find_deal_begin(self, df: pd.DataFrame):
        """
            This function finds the start of a deal based on certain conditions.
            # observe_begin: The date of the start of the signal
            # observe_deal_profit: The profit of the signal
            # observe_hit_profit: The change of the profit between the signal and now
            # observe_pattern: The pattern of the observe_begin
            pdx: hit: present
            pdy: deal: past

            Parameters:
            df (pd.DataFrame): The input DataFrame containing the data.

            Returns:
            None. The function modifies the input DataFrame in-place.
            """

        def process_group(group):
            try:
                for idx, signal in group.iterrows():
                    if signal['filter'].startswith('_'):
                        valid_pd = group.loc[idx:]
                        cutloss_df = valid_pd[~pd.isna(valid_pd['cutloss_time'])]
                        if not cutloss_df.empty:
                            lasted_cutloss_index = cutloss_df.index[0] - 1
                            data = valid_pd.loc[:lasted_cutloss_index].tail(1)
                            cutloss_times = sorted(cutloss_df['cutloss_time'].unique())

                            times = 1
                            for i in range(len(cutloss_times) - 1):
                                start = cutloss_times[i]
                                end = cutloss_times[i + 1]
                                exist = valid_pd[(valid_pd['time'] > start) & (valid_pd['time'] < end)]

                                if not exist.empty:
                                    times += 1
                            group.loc[idx, 'observe_time_cutloss'] = times

                        else:
                            data = valid_pd.tail(1)
                            group.loc[idx, 'observe_time_cutloss'] = 0

                        group.loc[idx, 'observe_hit_profit'] = (((signal['curr_close'] / signal['Open_1D']) - 1) * 100)
                        if not data.empty:
                            group.loc[idx, 'observe_begin'] = data['time'].iloc[0]
                            group.loc[idx, 'observe_deal_profit'] = (
                                    (data['curr_close'].iloc[0] / data['Open_1D'].iloc[0] - 1) * 100)

                    elif signal['filter'].startswith('~'):
                        data = group.tail(1)
                        group.loc[idx, 'observe_hit_profit'] = ((1 - (signal['curr_close'] / signal['Open_1D'])) * 100)
                        if not data.empty:
                            group.loc[idx, 'observe_begin'] = data['time'].iloc[0]
                            group.loc[idx, 'observe_deal_profit'] = (
                                    (1 - (data['curr_close'].iloc[0] / data['Open_1D'].iloc[0])) * 100)
            except Exception as error:
                print(f"Error find_deal_begin ticker {group['ticker'].iloc[0]}: {str(error)}")
            return group

        df[['observe_hit_profit', 'observe_time_cutloss']] = np.nan
        df[['observe_deal_profit', 'observe_begin']] = None
        result = df.groupby(['ticker', 'filter'], group_keys=False).apply(process_group).reset_index(drop=True)

        return result

    def _find_range_price_buy_pattern(self, df: pd.DataFrame):
        df[['upper_price']] = np.nan
        for i in range(df.shape[0]):
            row = df.iloc[i]
            ticker = row['ticker']
            filter = row['filter']
            if filter.startswith('~'):
                continue

            try:
                formula = self.filter.get(filter)
                lower_price, upper_price = utils.revert_indicator_v1(row, formula)
                upper_price = min(upper_price, row['Close'] * 1.1)
                df.loc[i, 'upper_price'] = upper_price
            except Exception as e:
                print(f"Error get range price ticker {ticker}: {str(e)}")

    def _alert_sell_pattern(self):
        results = []

        df_futures = [app.send_task('tasks.data_tasks.process_stock_with_range',
                                    kwargs={"ticker": ticker, "start_range": -10, "end_range": 0, "step": 1,
                                            "amount_data": 1}) for ticker in self.watch_for_sell_tickers]
        for df_future in df_futures:
            try:
                df_future = pd.DataFrame(df_future.get())
                for f in self.filter:
                    try:
                        if not (f.startswith('~')) or (f in utils.filter_functions):
                            continue
                        pdx = df_future.query(f'({self.filter[f]})').sort_values('time', ascending=False).copy()
                        pdx['filter'] = f
                        results.append(pdx)
                    except:
                        continue
            except Exception as e:
                print(f"Error processing ticker : {str(e)}")
                continue

        # df_future = pd.read_csv('df_future.csv')
        # for f in self.filter:
        #     try:
        #         if not (f.startswith('~')) or (f in utils.filter_functions):
        #             continue
        #         pdx = df_future.query(f'({self.filter[f]})').sort_values('time', ascending=False).copy()
        #         pdx['filter'] = f
        #         results.append(pdx)
        #     except:
        #         continue

        result = [r for r in results if not r.empty]
        result = pd.concat(results) if len(result) > 0 else None
        if result is not None:
            pd_n = result[result['Change'] < 0].copy()
            pd_p = result[result['Change'] > 0].copy()

            pd_n = pd_n.sort_values('Change', ascending=False).copy()
            pd_p = pd_p.sort_values('Change', ascending=True).copy()

            pd_n = pd_n.drop_duplicates(subset=['ticker', 'filter'], keep='first')
            pd_p = pd_p.drop_duplicates(subset=['ticker', 'filter'], keep='first')
            df = pd.concat([pd_n, pd_p], axis=0).sort_values('ticker')
            df = df.drop_duplicates(subset=['filter'], keep='first').reset_index(drop=True)
        else:
            return []

        dict_return = []
        for i in range(df.shape[0]):
            dict_return.append({
                'ticker': df.iloc[i]['ticker'],
                'filter': df.iloc[i]['filter'],
                'current': self.convert_to_kvnd_notation(df.iloc[i]['Close_Current']),
                'alert_price': self.convert_to_kvnd_notation(df.iloc[i]['Close'])
            })

        with open("alert_sell_pattern.json", "w") as json_file:
            json.dump(dict_return, json_file, indent=4)

        return dict_return

    @staticmethod
    def merge_ticker_by_name(dict_list):
        merged_dict = {}

        for item in dict_list:
            ticker = item.pop('ticker')
            if ticker not in merged_dict:
                merged_dict[ticker] = {'ticker': ticker, 'df': {item["filter"]: item['df']}}

            else:
                if item["filter"] not in merged_dict[ticker]['df']:
                    merged_dict[ticker]['df'][item["filter"]] = item['df']
                else:
                    merged_dict[ticker]['df'][item["filter"]] = pd.concat(
                        [merged_dict[ticker]['df'][item["filter"]], item['df']])
                # merged_dict[ticker]['df'].append(item['df'])
        return list(merged_dict.values())

    def _get_least_k_signals(self, evaluation_data):
        datetime.now()
        one_week_ago = datetime.now() - timedelta(days=8)

        evaluation_data = evaluation_data.drop_duplicates(subset=['filter', 'ticker'], keep='first')
        evaluation_data = evaluation_data[evaluation_data['time'] >= one_week_ago.strftime('%Y-%m-%d')].copy()

        least_k_signals = []
        least_k_buy_signals = []

        buy_signals = evaluation_data[evaluation_data['filter'].str.startswith('_')]
        sell_signals = evaluation_data[evaluation_data['filter'].str.startswith('~')].sort_values(
            ['filter', "time", "ticker"], ascending=[True, False, True]).reset_index(drop=True)

        if not buy_signals.empty:
            today_signals = buy_signals["time"].str.contains(datetime.now().strftime('%Y-%m-%d'))
            least_k_buy_signals.append(buy_signals[today_signals])
            buy_signals = buy_signals[~today_signals]
            least_k_buy_signals.append(buy_signals.groupby('filter').head(10))

            least_k_signals.append(pd.concat(least_k_buy_signals, axis=0).sort_values(['filter', "time", "ticker"],
                                                                                      ascending=[True, False, True]))

        if not sell_signals.empty:
            sell_obsever_signals = sell_signals[sell_signals['ticker'].isin(self.watch_for_sell_tickers)]
            sell_other_signals = sell_signals[~sell_signals['ticker'].isin(self.watch_for_sell_tickers)]
            least_k_signals.append(pd.concat([sell_obsever_signals, sell_other_signals], axis=0))

        return pd.concat(least_k_signals, axis=0).reset_index(drop=True)

    def _find_cutloss_signals(self, df):
        """
        Find signals that appeared in the last 6 months that were cutlossed
        """

        cutloss_df = df[~pd.isna(df['cutloss_id'])]
        cutloss_df = cutloss_df.drop_duplicates(subset=['ticker', 'filter', 'cutloss_id'], keep='first')
        cutloss_df = cutloss_df.sort_values(['ticker', 'time', 'filter'], ascending=[True, False, True])

        result = []
        for i in range(cutloss_df.shape[0]):
            if cutloss_df.iloc[i]['filter'].startswith('~'):
                continue

            result.append({
                'ticker': cutloss_df.iloc[i]['ticker'],
                'filter': cutloss_df.iloc[i]['filter'],
                'time': cutloss_df.iloc[i]['time'],
                'price': cutloss_df.iloc[i]['Open_1D'],
                'cutloss_time': cutloss_df.iloc[i]['cutloss_time'],
                'cutloss_price': cutloss_df.iloc[i]['cutloss_close'],
            })

        return result

    def render_html_content(self, d_render):
        with open(self.html_sample, 'r') as f:
            template = Template(f.read())
        html_content = template.render(**d_render)
        return html_content

    def build_report(self, processed_tickers):
        print("START")
        ymd = datetime.now().strftime('%B %d, %Y')
        f_indicator = self._get_filter_indicators()
        print("evaluate_filters")
        eval_pd, lastest_rows_pd = self._evaluate_filters(processed_tickers)
        # eval_pd = pd.read_csv('eval_pd.csv')
        # lastest_rows_pd = pd.read_csv('last_rows.csv')

        eval_pd = self._find_deal_begin(eval_pd)
        least_k_signals = self._get_least_k_signals(eval_pd)
        self._find_range_price_buy_pattern(least_k_signals)

        cutlossed_signals = self._find_cutloss_signals(eval_pd)

        print("alert_sell_pattern")
        sell_alert = self._alert_sell_pattern()
        # with open("alert_sell_pattern.json", "r") as json_file:
        #     sell_alert = json.load(json_file)
        _sell_alert = sell_alert.copy()
        for alert in _sell_alert:
            if least_k_signals.query(f"ticker == '{alert['ticker']}' & filter == '{alert['filter']}'").shape[0] > 0:
                sell_alert.remove(alert)

        filtered_tickers = []
        latest_filtered_tickers = []

        for _, row in least_k_signals.sort_values(['time', 'score', 'ticker', 'filter'],
                                                  ascending=[False, False, True, True]).iterrows():
            if row['filter'].startswith('_') and row['observe_begin'] is not None and (
                    (pd.to_datetime(row['time']) - pd.to_datetime(row['observe_begin'])).days <= 5):
                continue
            filtered_tickers.append({
                'filter': row['filter'],
                'time': row['time'],
                'begin': row['observe_begin'] if row['observe_begin'] is not None else 'N/A',
                'num_of_cutloss': row['observe_time_cutloss'],
                'hit_profit_today': self.convert_to_sci_notation(row['observe_hit_profit']),
                'deal_profit_today': self.convert_to_sci_notation(row['observe_deal_profit']),
                'current_price': self.convert_to_kvnd_notation(row['curr_close']),
                'current_volume': self.convert_to_sci_notation(row['curr_volume']),
                'hit_price': self.convert_to_kvnd_notation(row['Close']),
                'hit2now_holding_time': (datetime.now() - datetime.strptime(row['time'], '%Y-%m-%d')).days,
                'upper_price': self.convert_to_kvnd_notation(row['upper_price']),
                'score': row['score'],
                'ticker': row['ticker'],
                'formula': self.filter[row['filter']],
                'df': row[f_indicator[row['filter']]],
                'l_df': [[k, self.convert_to_sci_notation(v)] for k, v in row[f_indicator[row['filter']]].items()]
            })

        least_signals = least_k_signals[least_k_signals['observe_begin'].notna()].copy()
        for _, row in least_signals.sort_values(['time', 'score', 'observe_begin', 'ticker', 'filter'],
                                                ascending=[False, False, False, True, True]).iterrows():
            if row['filter'].startswith('_') and row['observe_begin'] is not None and (
                    (pd.to_datetime(row['time']) - pd.to_datetime(row['observe_begin'])).days <= 5):
                latest_filtered_tickers.append({
                    'filter': row['filter'],
                    'time': row['time'],
                    'begin': row['observe_begin'] if row['observe_begin'] is not None else 'N/A',
                    'num_of_cutloss': row['observe_time_cutloss'],
                    'hit_profit_today': self.convert_to_sci_notation(row['observe_hit_profit']),
                    'deal_profit_today': self.convert_to_sci_notation(row['observe_deal_profit']),
                    'current_price': self.convert_to_kvnd_notation(row['curr_close']),
                    'current_volume': self.convert_to_sci_notation(row['curr_volume']),
                    'hit_price': self.convert_to_kvnd_notation(row['Close']),
                    'hit2now_holding_time': (datetime.now() - datetime.strptime(row['time'], '%Y-%m-%d')).days,
                    'upper_price': self.convert_to_kvnd_notation(row['upper_price']),
                    'score': row['score'],
                    'ticker': row['ticker'],
                    'formula': self.filter[row['filter']],
                    'df': row[f_indicator[row['filter']]],
                    'l_df': [[k, self.convert_to_sci_notation(v)] for k, v in
                             row[f_indicator[row['filter']]].items()]
                })

        alert = {}
        alert['obs_ticker'] = [ticker for ticker in self.watch_for_sell_tickers if
                               ticker in self.missing_used_indicators_tickers]
        alert['over_threshold'] = len(self.missing_indicators_tickers)

        buy_recommend_tickers = list(
            set([i["ticker"] for i in latest_filtered_tickers if i['filter'].startswith('_')] +
                [i["ticker"] for i in filtered_tickers if i['filter'].startswith('_')]))
        d_render = {
            "today": ymd,
            "alert": alert,
            "filtered_signals": filtered_tickers,
            "latest_filtered_tickers": latest_filtered_tickers,
            "sell_alerts": sell_alert,
            "cutlossed_signals": cutlossed_signals,
            "list_watchlist_tickers": self.watch_for_sell_tickers,  # TickerList sheet
            "watchlist_tickers": str(self.watch_for_sell_tickers).replace("'", ""),  # TickerList sheet
            "ind_description": DICTIONARY,
            "buy_recommend_tickers": buy_recommend_tickers,
            "monitor_tickers": str(list(self.missing_indicators_tickers.keys())).replace("'", ""),
            "monitor": self.missing_indicators_tickers,  # Not in TickerBlacklist
            "monitor_null_nessary_indicator_tickers": self.missing_used_indicators_tickers,
            'index_report': self._evaluate_index(lastest_rows_pd)
        }

        html_content = self.render_html_content(d_render)
        with open('assets/content.html', 'w') as f:
            f.write(html_content)

        # filtered_tickers = self.merge_ticker_by_name(filtered_tickers)
        # for ticker in filtered_tickers:
        #     pdfs.append(self.pdf_report(ticker["ticker"]).build(self.filter, list(ticker["df"].values())))
        # utils.merge_pdfs(pdfs, self.output_path)

        return html_content, d_render


def post_report_to_api_service(report_data: dict) -> None:
    api_client = APIClient(base_url="http://localhost:8502/api/v1", api_key="tav2")
    data = []
    today = datetime.now().strftime("%Y-%m-%d")

    for category in ['filtered_signals', 'latest_filtered_tickers']:
        for recommend in report_data.get(category, []):
            try:
                volume = float(recommend['current_volume'].replace('e', 'E')) if recommend[
                                                                                     'current_volume'] != 'N/A' else 0.0
            except:
                volume = 0.0

            try:
                price = float(recommend['current_price'].replace('k', '000')) * 1000 if not pd.isna(
                    recommend['current_price']) else 0.0
            except:
                price = 0.0

            if recommend['time'] == today:
                data.append({
                    "signal": "buy" if recommend['filter'].startswith('_') else 'sell',
                    "ticker": recommend['ticker'],
                    "hit_date": recommend['time'],
                    "pattern": recommend['filter'],
                    "median_volume_1m": volume,
                    "close_price": price,
                    "score": float(recommend['score']),
                })

    max_retries = 3
    retry_delay = 1  # seconds

    for attempt in range(max_retries):
        try:
            result = api_client.insert_recommends(recommend_data_list=data)
            if result:
                print('Inserted successfully')
                break
        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
            else:
                print("Failed to insert data after maximum retries")


def main():
    FPATH = "../ticker_v1a/"
    ymd = datetime.now().strftime('%B %d, %Y')
    stock_list = pd.DataFrame(app.send_task('tasks.data_tasks.get_stock_list').get())
    watchlist_for_sell = stock_list[stock_list['is_watchlist'] == True]['ticker'].to_list() + ['VNINDEX']
    monitor_tickers = stock_list[stock_list['is_monitor'] == True]['ticker'].to_list()
    #
    # pass
    processed_tickers = [file.replace('.csv', '') for file in os.listdir(FPATH) if file.endswith('.csv')]
    used_indicators = utils.get_indicator_in_report_filter()
    # generator = ReportGenerator(watch_for_sell_tickers=watchlist_for_sell, alert_monitor_tickers={},
    #                             used_indicators=used_indicators, profile_monitor_tickers={}, f_path=FPATH)
    # report_content, report_data = generator.build_report(processed_tickers)

    monitor_service = utils.MonitorService(blacklist=None)
    path_report = monitor_service.monitor(observant_tickers=monitor_tickers, past_time='2025-01-01')
    alert_monitor, _ = monitor_service.null_tickers()
    email_monitor, email_monitor_path = monitor_service.null_indicators_in_ticker(used_indicators)

    generator = ReportGenerator(watch_for_sell_tickers=watchlist_for_sell, alert_monitor_tickers=alert_monitor,
                                used_indicators=used_indicators, profile_monitor_tickers=email_monitor, f_path=FPATH)
    report_content, report_data = generator.build_report(processed_tickers)

    # path_report = os.path.join("assets", f"monitor_report.csv")
    # email_monitor_path = os.path.join("assets", f"necessary_indicators_monitor.csv")
    # report_content = open('assets/content.html', 'rb').read().decode('utf-8')

    email_service = EmailServices()
    email_service.send(
        to_emails=['<EMAIL>', '<EMAIL>', '<EMAIL>',
                   '<EMAIL>', '<EMAIL>'],
        subject=f'Stock report on {ymd} by Kaffa',
        html_content=report_content,
        attachments=[
            email_service.csv_attachment(path_report),
            email_service.csv_attachment(email_monitor_path)

        ]
    )

    post_report_to_api_service(report_data)


if __name__ == '__main__':
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)
    current_dir = current_dir.replace("/report", "")

    main()
